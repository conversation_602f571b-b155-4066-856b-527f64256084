import json
from datetime import datetime

import dash
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
from dash import Output, Input, State, MATCH, set_props, ALL

from dash_app.widgets.date_picker import date_ranges
from dash_app.widgets.info_row_cards import *
from models.order import BuySell
from models.trade import TradeStatus, TradeDirection
from trades_db import TradesDB


def get_strategies():
    import sys
    print(sys.executable)

    strats = TradesDB.getStrategiesList()
    stratNames = [obj.name for obj in strats]
    return stratNames


def parse_strategy_ids(csv_ids):
    if not csv_ids:
        return []
    return [int(s.strip()) for s in csv_ids.split(",") if s.strip().isdigit()]


def format_trade_details(trade):
    name_list = []
    place_holder = None
    for strat_id in parse_strategy_ids(trade.strategy):
        if place_holder is None:
            place_holder = TradesDB.getStrategyById(strat_id).name
        name_list.append(TradesDB.getStrategyById(strat_id).name)
    strategy_dropdown = html.Div(
        [
            dcc.Dropdown(
                options=get_strategies(),
                placeholder=place_holder,
                value=name_list,
                multi=True,
                id={"type": "strategy-dropdown", "index": trade.trade_id}
            )
        ]
    )
    return html.Div([
        # html.H5(f"{trade.symbol} ({trade.direction.value})", className="mb-3"),
        dbc.Row([
            # Column 1 – Position Exchange, Dates, Status Info
            dbc.Col([
                html.H3("Time & Place"),
                html.Div([html.Strong("Status: "), trade.status.value]),
                html.Div([html.Strong("Exchange: "), trade.exchange.value]),
                html.Div([html.Strong("Opened: "), helper.formatDate(trade.timeOpen)]),
                html.Div([html.Strong("Closed: "), helper.formatDate(trade.timeClose) if trade.timeClose else "—"]),
                html.Div([html.Strong("Duration: "), trade.getTradeDurationString()]),
                html.Div([html.Strong("Last Update: "), helper.formatDate(trade.lastUpdate)]),
            ], width=4, className="mb-4"),

            # Column 2 – Position Price and Quantity Info
            dbc.Col([
                html.H3("Position Info"),
                html.Div([html.Strong("Quantity: "), trade.tradeQty]),
                html.Div([html.Strong("Open Qty: "), trade.openQty]),
                html.Div([html.Strong("Avg Open Price: "), "$" + str(trade.avgOpenPrice)]),
                html.Div([html.Strong("Avg Close Price: "), "$" + str(trade.avgClosePrice)]),
                html.Div([html.Strong("Notional: "), "$" + str(trade.notional)]),
                html.Div([html.Strong("Leverage: "), trade.leverage]),
            ], width=4),

            # Column 3 – Risk & Performance
            dbc.Col([
                html.H3("Risk & Performance"),
                html.Div([html.Strong("Risk %: "), trade.riskPercent]),
                html.Div([html.Strong("Risk Amount: "), "$" + str(trade.riskAmt)]),
                html.Div([html.Strong("Account Balance: "), "$" + str(trade.accountBalance)]),
                html.Div([html.Strong("Profit: "), "$" + str(trade.profit)]),
                html.Div([html.Strong("Fees: "), "$" + str(trade.fees)]),
            ], width=4),
        ]),
        dbc.Row([
            # New Row – Notes & Chart
            dbc.Col([
                html.H3("Notes"),
                html.Div(
                    id={"type": "trade-notes", "index": trade.trade_id},
                    children=_format_notes_with_delete_buttons(trade.notes, trade.trade_id),
                    style={"minHeight": "100px", "marginBottom": "10px"}
                ),
                dcc.Textarea(
                    id={"type": "notes-input", "index": trade.trade_id},
                    style={"width": "100%", "height": 35},
                ),
                dbc.Button("Save Notes", id={"type": "save-notes-btn", "index": trade.trade_id}, color="primary",
                           size="sm", className="mt-2", n_clicks=0),
            ], width=7),
            dbc.Col([
                html.Div([
                    html.H3("Strategy")
                ]),
                strategy_dropdown,
                html.Span(id={"type": "strategy-output", "index": trade.trade_id}, style={"verticalAlign": "middle"}),
            ], width=5),

        ])
    ], className="p-3")


def get_chart_data(trade_list):
    profit = 0
    trade_data = []
    longs_count = 0
    for trade in trade_list:
        if trade.profit is not None:
            profit += trade.profit
            trade_data.append({
                'profit': profit,
                'timeOpen': trade.timeOpen  # Convert MS to datetime
            })
    profit_frame = pd.DataFrame(trade_data)
    profit_frame.set_index('timeOpen', inplace=True)
    total_trades = len(profit_frame["profit"])
    win_rate = (np.sum(np.array(profit_frame["profit"]) > 0) /
                len(profit_frame["profit"])) * 100
    win_rate = round(float(win_rate))
    return profit_frame, total_trades, win_rate


# ================================
# Dash Layout
# ================================

def getAccordionItems(trade_list):
    if trade_list is None:
        return None, None
    items = []
    for t in trade_list:
        row = dbc.Row(
            [
                dbc.Col(html.Div(t.status.name)),
                dbc.Col(html.Div(t.symbol)),
                dbc.Col(html.Div(helper.format_decimal(t.tradeQty))),
                dbc.Col(html.Div(helper.formatDate(t.timeClose))),
                dbc.Col(html.Div(t.getTradeDurationString())),
                dbc.Col(html.Div(t.direction.value)),
                dbc.Col(html.Div("$" + t.notionalString())),
                dbc.Col(html.Div("$" + t.profitString())),
                dbc.Col(html.Div("$" + t.feesString())),
            ],
            className="m-0 p-0"
        )
        items.append(
            dbc.AccordionItem(
                children=html.Div(id={"type": "accord-content", "index": t.trade_id}),
                item_id={"type": "active-item", "index": t.trade_id},
                className="m-0 p-0",
                title=html.Div(
                    [row, html.Div(id={"type": "accord-div", "index": t.trade_id})],
                    style={"width": "100%", "cursor": "pointer"},
                    className="m-0 p-0 fs-7 fw-light"
                )
            )
        )
    return items, trade_list


# ================================
# Dash Callbacks
# ================================

def get_callbacks(app):
    # @app.callback(
    #     Output({"type": "save-status", "index": MATCH}, "children"),
    #     Input({"type": "save-notes-btn", "index": MATCH}, "n_clicks"),
    #     # State({"type": "trade-id", "index": MATCH}, "data"),
    #     prevent_initial_call=True, allow_duplicate=True
    # )
    # def save_notes(notes_text, trade_id):
    #     # You can handle saving to DB here
    #     print("Saving notes:", notes_text)  # Replace with DB save logic
    #     return "Notes saved!"  # Optional confirmation message

    # TradesDB.updateTradeNotes(trade_id=trade_id, trade_notes=trade_notes_input)
    # return dash.no_update

    # @app.callback(
    #     Output("save-status", "children"),
    #     Input("save-notes-btn", "n_clicks"),
    #     State("notes-input", "value"),
    #     prevent_initial_call=True
    # )
    # def save_notes(n_clicks, notes_text):
    #     # You can handle saving to DB here
    #     print("Saving notes:", notes_text)  # Replace with DB save logic
    #     return "Notes saved!"  # Optional confirmation message

    @app.callback(
        [Output("pnl_graph", 'figure'),
         Output("accord", 'children'),
         Output("infoRow", 'children')],
        Input("trade-search", "n_clicks"),
        ## Saved Search Bar Data ##
        State("selected-dates", "data"),
        State({"type": "crypto-checklist", "index": dash.ALL}, "value")
    )
    def trade_search(n_clicks, selected_dates, selected_symbols):
        start_date, end_date = (
            datetime.strptime(date_str, "%Y-%m-%d").date() if date_str else None
            for date_str in (selected_dates or [None, None])
        )

        # Get last saved search dates from cookies or store
        if start_date is None:
            start_date, end_date = date_ranges.get("last-90")

        if start_date and end_date is not None:
            start_date = start_date.strftime("%Y-%m-%d")
            end_date = end_date.strftime("%Y-%m-%d")
            trade_list = TradesDB.get_trades(startDate=start_date,
                                             endDate=end_date,
                                             symbol_list=selected_symbols)
            if len(trade_list) > 0:
                items, trade_list = getAccordionItems(trade_list)
                # Closed orders only from profit time series
                profit_frame, total_trades, win_rate = get_chart_data(trade_list)
                # px.data.stocks(indexed=True)-1
                fig = px.area(profit_frame)
                # fig.update_layout(plot_bgcolor='rgba(0, 0, 0, 0)', paper_bgcolor='rgba(0, 0, 0, 0)')

                wins = sum(1 for trade in trade_list if trade.profit > 0 and trade.status == TradeStatus.CLOSED)
                losses = sum(1 for trade in trade_list if trade.profit < 0 and trade.status == TradeStatus.CLOSED)

                average_profit = sum(trade.profit for trade in trade_list if
                                     trade.profit is not None and trade.status == TradeStatus.CLOSED) / \
                                 sum(1 for trade in trade_list if
                                     trade.profit is not None and trade.status == TradeStatus.CLOSED) if trade_list else 0

                average_duration = sum(trade.duration for trade in trade_list if
                                       trade.duration is not None and trade.status == TradeStatus.CLOSED) / \
                                   sum(1 for trade in trade_list if
                                       trade.duration is not None and trade.status == TradeStatus.CLOSED) if trade_list else 0

                long_trades = sum(1 for trade in trade_list if trade.direction == TradeDirection.LONG)
                short_trades = sum(1 for trade in trade_list if trade.direction == TradeDirection.SHORT)
                long_percentage = round((long_trades / len(trade_list)) * 100)
                short_percentage = round((short_trades / len(trade_list)) * 100)

                infoRow = (dbc.Col(get_total_trades_card(len(trade_list), wins, losses), width=3),
                           dbc.Col(get_win_rate_card(win_rate, len(trade_list), wins, losses), width=3),
                           dbc.Col(get_average_trade_time_card(average_duration), width=3),
                           dbc.Col(
                               get_long_short_ratio_card(long_percentage, short_percentage, long_trades, short_trades),
                               width=3))

                return (fig,
                        items,
                        infoRow
                        )
            else:
                return dash.no_update, dash.no_update, dash.no_update
        return None

    @app.callback(
        Input("accord", "active_item"),
        prevent_initial_call=True
    )
    def active_item(active):
        if active is None:
            return
        trade_id = active["index"]
        set_props({'type': 'accord-div', 'index': trade_id}, {"n_clicks": "7"})

    # We use set_props here to open row and add/update row contents to reduce overhead and extra callbacks
    # @app.callback(
    #     Input("accord", "active_item"),
    #     prevent_initial_call=True
    #
    # Original way of using Output for rowOpened
    @app.callback(
        Output({'type': 'accord-content', 'index': MATCH}, 'children'),
        Input({'type': 'accord-div', 'index': MATCH}, 'n_clicks'),
        State({'type': 'accord-content', 'index': MATCH}, 'children'),
        prevent_initial_call=True
    )
    def rowOpened(n_clicks, children):
        if children is not None:
            return dash.no_update
        trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]

        # if active_item is None:
        #     return
        # trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]
        # if n_clicks > 1:
        #     return dash.no_update
        # trade_id = active_item["index"]
        trade = TradesDB.get_trade_by_id(trade_id=trade_id)

        # TRADE GRAPH SECTION
        fig = getTradeGraph()
        fig.update_layout(
            height=150,
            width=300,
            margin=dict(l=0, r=0, t=0, b=0)  # Set the bottom margin to 100 pixels
        )

        trade_graph = dcc.Graph(id={"type": "graph", "index": 1},
                                figure=fig,
                                config={'scrollZoom': True,
                                        'modeBarButtonsToRemove': ['lasso', 'select', 'zoom']}
                                )

        # Sort so oldest (opening order) is first in list
        # trade.sortOrders()
        row_data = dbc.Container(
            [
                dbc.Row(
                    [
                        dbc.Col([
                            trade_graph,
                            html.A("View TV Chart", href="#" or trade.chartLink,
                                   target="_blank", className="btn btn-outline-primary btn-sm mt-2")
                        ], width=4),
                        dbc.Col(format_trade_details(trade), width=8)],
                ),
                dbc.Row(
                    [
                        dbc.Col(generate_trades_table(trade.trade_orders), width=12)
                    ], className="padding-top padding-bottom")
            ], fluid=True
        )
        # set_props({'type': 'accord-content', 'index': trade_id}, {'children': button_test, 'loading_state': ""})
        return row_data

    @app.callback(
        Output({"type": "trade-notes", "index": MATCH}, "children"),
        Output({"type": "notes-input", "index": MATCH}, "value"),
        Input({"type": "save-notes-btn", "index": MATCH}, "n_clicks"),
        State({"type": "notes-input", "index": MATCH}, "value"),
        prevent_initial_call=True
    )
    def save_notes_click(n_clicks, value):
        trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]
        notes = TradesDB.save_trade_note(trade_id, value)
        return notes, ""

    # @app.callback(
    #     Output({"type": "strategy-dropdown", "index": MATCH}, "options"),
    #     Input({"type": "strategy-dropdown", "index": MATCH}, "search_value"),
    # )
    # def search_strategies_dropdown(search_value):
    #     if search_value is None or search_value == '' or search_value == ' ':
    #         return dash.no_update
    #     stratList = []
    #     if search_value in stratList:
    #         print("Already have strat")

    @app.callback(
        # Output({"type": "strategy-output", "index": MATCH}, "children"),
        Input({"type": "strategy-dropdown", "index": ALL}, "value"),
        suppress_callback_exceptions=True, prevent_initial_call=True)
    def save_strategy(strat_name):
        if len(strat_name[0]) == 0:
            return
        trade_id = json.loads(dash.ctx.triggered[0]["prop_id"].split(".")[0])["index"]
        TradesDB.save_trade_strategy(trade_id, strat_name[0])
        # return f'Updated'

    ###########################
    # Helper Functions
    ###########################

    def getTradeGraph():
        df = pd.read_csv('https://raw.githubusercontent.com/plotly/datasets/master/finance-charts-apple.csv')
        fig = go.Figure(go.Candlestick(
            x=df['Date'],
            open=df['AAPL.Open'],
            high=df['AAPL.High'],
            low=df['AAPL.Low'],
            close=df['AAPL.Close'],
        ))
        fig.update_layout(dragmode='drawrect',
                          xaxis_rangeslider_visible=False)
        return fig

    def generate_trades_table(orders):
        return html.Table(
            # Table with headers and rows
            children=[
                # Table Header
                html.Thead(
                    html.Tr([
                        html.Th("Created Date"),
                        html.Th("Filled Date"),
                        html.Th("Order Type"),
                        html.Th("Buy/Sell"),
                        html.Th("Quantity"),
                        html.Th("Price"),
                        html.Th("Order Status")
                    ])
                ),
                # Table Body
                html.Tbody([
                    html.Tr(
                        [
                            html.Td(helper.formatDate(order.created_date)),
                            html.Td(helper.formatDate(order.filled_date)),
                            html.Td(order.orderType),
                            html.Td(str(order.buySell.value)),
                            html.Td(str(order.quantity)),
                            html.Td(f"${order.price:.2f}"),  # Format price
                            html.Td(order.orderStatus),
                        ],
                        style={
                            # Color logic: Yellow for unfilled orders, Green for BUY, Red for SELL
                            "backgroundColor": "#fff3cd" if not order.filled else
                            "#d4edda" if order.buySell == BuySell.BUY else "#f8d7da",
                            "color": "#856404" if not order.filled else
                            "#155724" if order.buySell == BuySell.BUY else "#721c24",
                        }
                    )
                    for order in orders
                ])
            ],
            style={"border": "1px solid black", "width": "100%", "borderCollapse": "collapse"},
        )

# @callback(
#     Output(component_id={'type': 'alert', 'index': MATCH}, component_property='children'),
#     Input(component_id={'type': 'graph', 'index': MATCH}, component_property='relayoutData'))
# def userInput(col_chosen):
#     print(col_chosen)
#     if col_chosen is not None:
#         x0 = str(col_chosen["shapes"][-1]["x0"])
#         x1 = str(col_chosen["shapes"][-1]["x1"])
#         y0 = str(col_chosen["shapes"][-1]["y0"])
#         y1 = str(col_chosen["shapes"][-1]["y1"])
#         return "Coordinates Are: " + x0 + " / " + y0 + "  -  " + x1 + " / " + y1
#     else:
#         return "None"


# @callback(
#     Output(component_id='graph', component_property='figure'),
#     Input(component_id='toggle-rangeslider', component_property='value'),
#     State(component_id='graph', component_property='figure'))
# def update_chart(value, figure):
#     if figure is None:
#         df = pd.read_csv('https://raw.githubusercontent.com/plotly/datasets/master/finance-charts-apple.csv')
#         figure = go.Figure(go.Candlestick(
#             x=df['Date'],
#             open=df['AAPL.Open'],
#             high=df['AAPL.High'],
#             low=df['AAPL.Low'],
#             close=df['AAPL.Close'],
#         ))
#         figure.update_layout(dragmode='drawrect',
#                              xaxis_rangeslider_visible=False)
#     else:
#         figure['layout']['xaxis']['rangeslider']['visible'] = False in value
#     return figure
