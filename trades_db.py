import json
import os
import sqlite3
import time
from datetime import datetime

import helper
from models import trade
from models.order import Order
from models.strategy import Strategy
from trades_db_tables import TradeDB_tables


class dot_dict(dict):
    __getattr__ = dict.get
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__


# Database Path Setup
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_DIR = os.path.normpath(os.path.join(BASE_DIR, "data"))
DB_PATH = os.path.join(DB_DIR, "trades.db")

# ✅ Ensure the database directory exists
if not os.path.exists(DB_DIR):
    os.makedirs(DB_DIR)

# ✅ Create a single shared database connection
_db_connection = None


def get_db_connection():
    """Get a single database connection, creating it if needed."""
    if not os.path.exists(DB_PATH):
        TradeDB_tables.create_db_and_tables()
    global _db_connection
    if _db_connection is None:
        _db_connection = sqlite3.connect(DB_PATH, check_same_thread=False)
    return _db_connection


def get_db_cursor():
    """Get a cursor from the shared connection."""
    return get_db_connection().cursor()


# Coinbase-Advanced-Trade API returns an OrderConfiguration object
# We will store it as json string in DB for simplicity
def object_to_dict(obj):
    """
    Recursively converts an object (including nested objects) to a dictionary.
    Handles objects with __dict__, lists, and dictionaries.
    """
    if isinstance(obj, dict):
        return {k: object_to_dict(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [object_to_dict(item) for item in obj]
    elif hasattr(obj, "__dict__"):  # Convert object attributes to dictionary
        return {k: object_to_dict(v) for k, v in obj.__dict__.items()}
    else:
        return obj  # Return primitive types (int, str, etc.) as-is


def object_to_json(obj):
    """Convert an object to a JSON string for database storage."""
    return json.dumps(object_to_dict(obj), indent=4)


class TradesDB:
    @staticmethod
    def check_db_exists():
        return os.path.exists(DB_PATH)

    @staticmethod
    def get_results(db_cursor):
        desc = [d[0] for d in db_cursor.description]
        results = [dot_dict(dict(zip(desc, res))) for res in db_cursor.fetchall()]
        return results

    # @staticmethod
    # def getProfitTimeSeries(startDate=None, endDate=None):
    #     cursor = get_db_connection().cursor()
    #     trade_data = []
    #     if startDate is not None:
    #         startDateMS = helper.dateStringToMS(startDate, date_format='%Y-%m-%d')
    #         endDateMS = helper.dateStringToMS(endDate + " 23:59:59", date_format='%Y-%m-%d %H:%M:%S')
    #         cursor.execute(
    #             'SELECT (profit - fees), timeClose FROM Trade where timeClose > ? and timeClose < ? ORDER BY '
    #             'timeClose asc',
    #             (startDateMS, endDateMS))
    #     else:
    #         cursor.execute('SELECT (profit - fees), timeClose  FROM Trade')
    #
    #     results = cursor.fetchall()
    #     if len(results) == 0:
    #         return None
    #
    #     profit = 0
    #     longs_count = 0
    #     for row in results:
    #         if row[1] is not None:
    #             profit += row[0]
    #             trade_data.append({
    #                 'profit': profit,
    #                 'timeOpen': helper.mSToDate(row[1])  # Convert MS to datetime
    #             })
    #     profit_frame = pd.DataFrame(trade_data)
    #     profit_frame.set_index('timeOpen', inplace=True)
    #     total_trades = len(profit_frame["profit"])
    #     win_rate = (np.sum(np.array(profit_frame["profit"]) > 0) /
    #                 len(profit_frame["profit"])) * 100
    #     win_rate = round(float(win_rate))
    #     return profit_frame, total_trades, win_rate

    @staticmethod
    def getBybitOrderIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT bybit_order_id FROM BybitOrder')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["bybit_order_id"])
        return orderList

    @staticmethod
    def getCoinbaseOrderIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT coinbase_order_id FROM CoinbaseOrder')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["coinbase_order_id"])
        return orderList

    @staticmethod
    def get_coinbase_last_order_time():
        last_order_time = None
        cursor = get_db_cursor()
        cursor.execute('SELECT created_time FROM CoinbaseOrder order by created_time desc limit 1')
        results = TradesDB.get_results(cursor)
        for row in results:
            last_order_time = helper.mSToDate(row["created_time"])
        return last_order_time

    @staticmethod
    def get_bybit_last_order_time():
        last_order_time = None
        cursor = get_db_cursor()
        cursor.execute('SELECT createdTime FROM BybitOrder order by createdTime desc limit 1')
        results = TradesDB.get_results(cursor)
        for row in results:
            last_order_time = helper.mSToDate(row["createdTime"])
        return last_order_time

    @staticmethod
    def getSierraActivityIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT activityId FROM SierraActivity')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["activityId"])
        return orderList

    @staticmethod
    def getStrategiesList():
        strategies = []
        cursor = get_db_cursor()
        cursor.execute('SELECT * FROM Strategy')
        results = TradesDB.get_results(cursor)
        for row in results:
            strat = Strategy.fromRow(row=row)
            strategies.append(strat)
        return strategies

    @staticmethod
    def getStrategyById(stratId):
        strategies = []
        cursor = get_db_cursor()
        query = "SELECT * FROM Strategy WHERE id = ?"
        cursor.execute(query, (stratId,))
        results = TradesDB.get_results(cursor)
        for row in results:
            strat = Strategy.fromRow(row=row)
            strategies.append(strat)
        return strategies[0]

    @staticmethod
    def get_trades(exchange=None, startDate=None, endDate=None, symbol_list=None, include_open_trades=True):
        tradeList = []
        cursor = get_db_cursor()

        query = '''
            SELECT *
            FROM Trade t
            INNER JOIN Orders o ON t.id = o.trade_id
        '''
        params = []
        if exchange:
            query += " WHERE t.exchange = ?"
            params = [exchange.value]

        # Add date filtering if startDate is provided
        if startDate is not None:
            startDateMS = helper.dateStringToMS(startDate, date_format='%Y-%m-%d')
            endDateMS = helper.dateStringToMS(endDate + " 23:59:59", date_format='%Y-%m-%d %H:%M:%S')

            # Modified date condition to handle open trades
            if include_open_trades:
                # Include trades that are open (timeClose = 0 or NULL) OR closed within date range
                if exchange:
                    query += " AND (t.timeOpen > ? AND (t.timeClose = 0 OR t.timeClose IS NULL OR t.timeClose < ?))"
                else:
                    query += " WHERE (t.timeOpen > ? AND (t.timeClose = 0 OR t.timeClose IS NULL OR t.timeClose < ?))"
            else:
                # Original behavior: only include trades closed within date range
                if exchange:
                    query += " AND (t.timeOpen > ? AND t.timeClose < ?)"
                else:
                    query += " WHERE (t.timeOpen > ? AND t.timeClose < ?)"

            params.extend([startDateMS, endDateMS])

        # Add symbol filtering if symbol_list is provided
        if symbol_list:
            flat_symbol_list = [symbol for sublist in symbol_list for symbol in sublist]  # Flattening
            placeholders = ', '.join(['?' for _ in flat_symbol_list])
            if exchange or startDate is not None:
                query += " AND t.symbol IN ({})".format(placeholders)
            else:
                query += " WHERE t.symbol IN ({})".format(placeholders)
            params.extend(flat_symbol_list)

        # Append ORDER BY clause
        query += '''
            ORDER BY 
              CASE 
                WHEN t.timeClose = 0 THEN 0  -- Prioritize rows with timeClose = 0
                ELSE 1                        -- All other rows
              END,
              t.timeClose DESC;
        '''

        cursor.execute(query, params)
        results = TradesDB.get_results(cursor)
        if len(results) == 0:
            return tradeList
        for row in results:
            order = Order.fromRow(row=row)
            th_trade = next((t for t in tradeList if t.trade_id == row["trade_id"]), None)
            if th_trade is not None:
                th_trade.trade_orders.append(order)
            else:
                tradeList.append(trade.Trade.fromDBRow(row=row, order=order))
        return tradeList

    @staticmethod
    def get_trade_by_id(trade_id):
        """ Retrieves a specific trade by its ID from the database. """
        cursor = get_db_cursor()

        cursor.execute(
            '''SELECT *
               FROM Trade t
               INNER JOIN Orders o ON t.id = o.trade_id
               WHERE t.id = ?''',
            (trade_id,)
        )

        results = TradesDB.get_results(cursor)
        if not results:
            return None  # Return None if no trade is found

        # Create the trade object from the first matching row
        row = results[0]
        th_trade = trade.Trade.fromDBRow(row=row, order=Order.fromRow(row=row))

        # Add additional orders related to the trade
        for row in results[1:]:
            th_trade.trade_orders.append(Order.fromRow(row=row))

        return th_trade

    @staticmethod
    def get_exchange_names():
        exchanges = []
        cursor = get_db_cursor()
        cursor.execute("SELECT DISTINCT exchange FROM Trade WHERE exchange IS NOT NULL")
        exchanges = [row[0] for row in cursor.fetchall()]

        return exchanges

    @staticmethod
    def get_symbols_list():
        symbols = []
        cursor = get_db_cursor()

        # Query to get distinct symbols within the date range
        cursor.execute("""
            SELECT DISTINCT symbol FROM Trade WHERE symbol IS NOT NULL""")

        symbols = [row[0] for row in cursor.fetchall()]

        return symbols

    @staticmethod
    def deleteStrategyById(stratId):
        cursor = get_db_cursor()
        cursor.execute('''DELETE FROM Strategy WHERE id=?''',
                       (stratId,))
        get_db_connection().commit()

    @staticmethod
    def oldestSavedOrder():
        getTime = None
        cursor = get_db_cursor()
        cursor.execute('SELECT updatedTime FROM BybitOrder ORDER BY updatedTime ASC limit 5')
        rows = cursor.fetchall()
        for row in rows:
            getTime = row[0]
        return getTime

    @staticmethod
    def newestSavedOrder():
        getTime = None
        cursor = get_db_cursor()
        cursor.execute('SELECT createdTime FROM BybitOrder ORDER BY createdTime DESC limit 5')
        rows = cursor.fetchall()
        for row in rows:
            getTime = row[0]
        return getTime

    @staticmethod
    def updateStrategy(strategy):
        cursor = get_db_cursor()
        cursor.execute('''UPDATE Strategy SET name=?, description=?, notes=?, images=?, 
                                    modifiedDate=? WHERE id=?''',
                       (strategy.name, strategy.description, strategy.notes, strategy.images,
                        time.time(), strategy.stratId))
        get_db_connection().commit()

    @staticmethod
    def updateTrade(trade):
        t = trade
        cursor = get_db_cursor()
        cursor.execute(
            'UPDATE Trade SET status = ?, tradeQty = ?, openQty = ?, timeOpen = ?, lastUpdate = ?, timeClose = ?, '
            'chartLink = ?, notes = ?, notional = ?, leverage = ?, avgOpenPrice = ?, avgClosePrice = ?, '
            'riskPercent = ?, riskAmt = ?, profit = ?, fees = ?, duration = ?, direction = ?, trade_id = ? where id = ?',
            (t.status.name, helper.store_decimal(t.tradeQty), helper.store_decimal(t.openQty),
             helper.date_to_ms(t.timeOpen), helper.date_to_ms(t.lastUpdate), helper.date_to_ms(t.timeClose),
             None, None, helper.store_decimal(t.notional), None, helper.store_decimal(t.avgOpenPrice),
             helper.store_decimal(t.avgClosePrice), helper.store_decimal(t.riskPercent),
             helper.store_decimal(t.riskAmt),
             helper.store_decimal(t.profit), helper.store_decimal(t.fees), t.duration, t.direction.value, t.trade_id,
             t.id_field))
        get_db_connection().commit()

        TradesDB.update_trade_orders(t)

    @staticmethod
    def update_trade_orders(t):
        for order in t.trade_orders:
            if order.id_field is None:
                order.trade_id = t.id_field
                TradesDB.saveOrder(od=order)
        # Loop over unfilled_orders and call saveOrder on each that do not have an id_field value
        for order in t.unfilled_orders:
            if order.id_field is None:
                order.trade_id = t.id_field
                TradesDB.saveOrder(od=order)

    @staticmethod
    def saveOrder(od: Order):
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO Orders (order_id, created_date, filled_date, symbol, orderType, orderStatus, buySell, reduce, price,
                                fillPrice, quantity, filledQuantity, trade_id, fee)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (od.order_id, helper.date_to_ms(od.created_date), helper.date_to_ms(od.filled_date), od.symbol,
                        od.orderType,
                        od.orderStatus, od.buySell.value, int(od.reduce), helper.store_decimal(od.price),
                        helper.store_decimal(od.fillPrice), helper.store_decimal(od.quantity),
                        helper.store_decimal(od.filledQuantity),
                        od.trade_id, helper.store_decimal(od.fee)))
        get_db_connection().commit()

        od.id_field = cursor.lastrowid

        # Save Activity if we have one
        if od.sierraActivity:
            od.sierraActivity.orderId = od.id_field
            TradesDB.saveActivity(activity=od.sierraActivity)
        elif od.coinbaseFill:
            od.coinbaseFill.orderId = od.id_field
            TradesDB.saveCoinbaseFill(fill=od.coinbaseFill)
        elif od.coinbaseOrder:
            od.coinbaseOrder.orderId = od.id_field
            TradesDB.saveCoinbaseOrder(cbo=od.coinbaseOrder)
        elif od.bybitOrder:
            od.bybitOrder.orderId = od.id_field
            TradesDB.saveBybitOrder(order=od.bybitOrder)

    @staticmethod
    def saveBybitOrder(order):
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO BybitOrder (
                        order_id, bybit_order_id, orderLinkId, blockTradeId, symbol,
                        price, qty, side, isLeverage, positionIdx, orderStatus,
                        createType, cancelType, rejectReason, avgPrice, leavesQty,
                        leavesValue, cumExecQty, cumExecValue, cumExecFee, timeInForce,
                        orderType, stopOrderType, orderIv, triggerPrice, takeProfit,
                        stopLoss, tpslMode, tpLimitPrice, slLimitPrice, tpTriggerBy,
                        slTriggerBy, triggerDirection, triggerBy, lastPriceOnCreated,
                        reduceOnly, closeOnTrigger, placeType, smpType, smpGroup,
                        smpOrderId, createdTime, updatedTime)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                               ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                               ?, ?, ?)''',
                       (order.orderId, order.bybit_order_id, order.orderLinkId, order.blockTradeId, order.symbol,
                        helper.store_decimal(order.price), helper.store_decimal(order.qty), order.side,
                        order.isLeverage, order.positionIdx, order.orderStatus,
                        order.createType, order.cancelType, order.rejectReason, helper.store_decimal(order.avgPrice),
                        helper.store_decimal(order.leavesQty),
                        helper.store_decimal(order.leavesValue), helper.store_decimal(order.cumExecQty),
                        helper.store_decimal(order.cumExecValue), helper.store_decimal(order.cumExecFee),
                        order.timeInForce,
                        order.orderType, order.stopOrderType, order.orderIv, helper.store_decimal(order.triggerPrice),
                        helper.store_decimal(order.takeProfit),
                        helper.store_decimal(order.stopLoss), order.tpslMode, helper.store_decimal(order.tpLimitPrice),
                        helper.store_decimal(order.slLimitPrice), order.tpTriggerBy,
                        order.slTriggerBy, order.triggerDirection, order.triggerBy,
                        helper.store_decimal(order.lastPriceOnCreated),
                        int(order.reduceOnly), int(order.closeOnTrigger), order.placeType, order.smpType,
                        order.smpGroup,
                        order.smpOrderId, helper.date_to_ms(order.createdTime), helper.date_to_ms(order.updatedTime)))
        get_db_connection().commit()

    @staticmethod
    def saveActivity(activity):
        act = activity
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO SierraActivity (orderId, activityId, activityType, date, transDate, symbol, orderActionSource,
                                quantity, orderType, buySell, price, price2, internalOrderId, serviceOrderId,
                                orderStatus, exchangeOrderId, fillPrice, filledQuantity, tradeAccount, openClose, parentInternalOrderId,
                                positionQuantity, fillExecutionServiceId, highDuringPosition, lowDuringPosition, note, accountBalance,
                                clientOrderId, timeInForce, isAutomated) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (act.orderId, act.activityId, act.activityType, helper.date_to_ms(act.date),
                        helper.date_to_ms(act.transDate), act.symbol,
                        act.orderActionSource,
                        helper.store_decimal(act.quantity), act.orderType, act.buySell, helper.store_decimal(act.price),
                        helper.store_decimal(act.price2),
                        act.internalOrderId, act.serviceOrderId,
                        act.orderStatus, act.exchangeOrderId, helper.store_decimal(act.fillPrice),
                        helper.store_decimal(act.filledQuantity),
                        act.tradeAccount, act.openClose, act.parentInternalOrderId,
                        helper.store_decimal(act.positionQuantity), act.fillExecutionServiceId,
                        helper.store_decimal(act.highDuringPosition), helper.store_decimal(act.lowDuringPosition),
                        act.note, helper.store_decimal(act.accountBalance),
                        act.clientOrderId, act.timeInForce, act.isAutomated))
        get_db_connection().commit()

    @staticmethod
    def saveCoinbaseOrder(cbo):
        cursor = get_db_cursor()
        insert_query = '''
            INSERT INTO CoinbaseOrder (
                coinbase_order_id, attached_order_configuration, attached_order_id, average_filled_price, cancel_message,
                client_order_id, completion_percentage, created_time, edit_history, fee, filled_size, 
                filled_value, is_liquidation, last_fill_time, leverage, margin_type, number_of_fills, order_configuration, 
                order_placement_source, order_type, originating_order_id, outstanding_hold_amount, pending_cancel, 
                product_id, product_type, reject_message, reject_reason, retail_portfolio_id, settled, side, 
                size_in_quote, size_inclusive_of_fees, status, time_in_force, total_fees, total_value_after_fees, trigger_status, 
                user_id, order_Id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

        if len(cbo.edit_history) > 0:
            print("have som")

        # Prepare the values from the cbo object
        values = (
            cbo.coinbase_order_id, cbo.attached_order_configuration, cbo.attached_order_id,
            helper.store_decimal(cbo.average_filled_price), cbo.cancel_message,
            cbo.client_order_id, helper.store_decimal(cbo.completion_percentage), helper.date_to_ms(cbo.created_time),
            object_to_json(cbo.edit_history),
            helper.store_decimal(cbo.fee), helper.store_decimal(cbo.filled_size),
            helper.store_decimal(cbo.filled_value),
            cbo.is_liquidation, helper.date_to_ms(cbo.last_fill_time), helper.store_decimal(cbo.leverage),
            cbo.margin_type,
            helper.store_decimal(cbo.number_of_fills), object_to_json(cbo.order_configuration),
            cbo.order_placement_source, cbo.order_type, cbo.originating_order_id, cbo.outstanding_hold_amount,
            cbo.pending_cancel, cbo.product_id, cbo.product_type, cbo.reject_message, cbo.reject_reason,
            cbo.retail_portfolio_id,
            cbo.settled, cbo.side, cbo.size_in_quote, cbo.size_inclusive_of_fees, cbo.status, cbo.time_in_force,
            helper.store_decimal(cbo.total_fees), helper.store_decimal(cbo.total_value_after_fees),
            cbo.trigger_status, cbo.user_id, cbo.order_id
        )
        # Execute the query
        cursor.execute(insert_query, values)
        get_db_connection().commit()

    @staticmethod
    def saveCoinbaseFill(fill):
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO CoinbaseOrder (order_id, commission, entry_id, liquidity_indicator, coinbase_order_id, price, product_id,
                                retail_portfolio_id, sequence_timestamp, side, size, size_in_quote, trade_id, trade_time, trade_type, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (fill.order_id, helper.store_decimal(fill.commission), fill.entry_id, fill.liquidity_indicator,
                        fill.coinbase_order_id, helper.store_decimal(fill.price), fill.product_id,
                        fill.retail_portfolio_id, helper.date_to_ms(fill.sequence_timestamp), fill.side,
                        helper.store_decimal(fill.size), helper.store_decimal(fill.size_in_quote),
                        fill.trade_id, helper.date_to_ms(fill.trade_time), fill.trade_type, fill.user_id))
        get_db_connection().commit()

    @staticmethod
    def saveStrategy(strategy):
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO Strategy (name, description, notes, images, createdDate, modifiedDate)
                                VALUES (?, ?, ?, ?, ?, ?)''',
                       (strategy.name, strategy.description, strategy.notes,
                        strategy.images, helper.date_to_ms(strategy.createdDate),
                        helper.date_to_ms(strategy.modifiedDate)))
        get_db_connection().commit()

    @staticmethod
    def saveTrade(trade):
        t = trade
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO Trade (status, tradeQty, openQty, timeOpen, lastUpdate, timeClose,
                                symbol, chartLink, notes, notional, leverage, avgOpenPrice, avgClosePrice,
                                riskPercent, accountBalance, riskAmt, profit, fees, exchange, duration, direction, username, trade_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (t.status.name, helper.store_decimal(t.tradeQty), helper.store_decimal(t.openQty),
                        helper.date_to_ms(t.timeOpen),
                        helper.date_to_ms(t.lastUpdate), helper.date_to_ms(t.timeClose),
                        t.symbol, None, None, helper.store_decimal(t.notional), None,
                        helper.store_decimal(t.avgOpenPrice),
                        helper.store_decimal(t.avgClosePrice),
                        helper.store_decimal(t.riskPercent), helper.store_decimal(t.accountBalance),
                        helper.store_decimal(t.riskAmt),
                        helper.store_decimal(t.profit), helper.store_decimal(t.fees), t.exchange.value, t.duration,
                        t.direction.value, t.username, t.trade_id))
        get_db_connection().commit()

        # Set new ID to trade
        t.id_field = cursor.lastrowid
        TradesDB.update_trade_orders(t)

    @staticmethod
    def save_trade_note(trade_id, new_note):
        now_str = helper.formatDate(helper.get_now_date())
        line = f"{now_str} {new_note}"

        # Fetch current notes
        cursor = get_db_cursor()
        cursor.execute("SELECT notes FROM Trade WHERE id = ?", (str(trade_id),))
        row = cursor.fetchone()
        current_notes = row[0] if row and row[0] else ""

        # Append new line
        updated_notes = f"{current_notes.strip()}\n{line}".strip()

        cursor.execute("UPDATE Trade SET notes = ? WHERE id = ?", (updated_notes, str(trade_id)))
        get_db_connection().commit()
        return updated_notes

    @staticmethod
    def save_trade_strategy(trade_id, strat_names):
        if not isinstance(strat_names, list):
            strat_names = [strat_names]

        cursor = get_db_cursor()

        # Lookup strategy IDs
        placeholder = ",".join("?" for _ in strat_names)
        cursor.execute(f"SELECT id FROM Strategy WHERE name IN ({placeholder})", strat_names)
        strat_ids = [str(row[0]) for row in cursor.fetchall()]

        # Get current strategies
        cursor.execute("SELECT strategy FROM Trade WHERE id = ?", (str(trade_id),))
        row = cursor.fetchone()
        current_ids = [s.strip() for s in str(row[0] or "").split(",") if s.strip()] if row else []

        # Merge and dedupe
        updated_ids = list(dict.fromkeys(current_ids + strat_ids))  # keeps order, removes dupes

        cursor.execute(
            "UPDATE Trade SET strategy = ? WHERE id = ?",
            (",".join(map(str, updated_ids)), str(trade_id))
        )
        get_db_connection().commit()
