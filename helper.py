import os
from datetime import datetime, timezone
from decimal import Decimal

import pandas as pd
import pytz
from dateutil import parser

ms_multiplier = 1000000


def check_decimal(value):
    if value is None:
        return None

    return Decimal(value)


def store_decimal(value):
    if value is None:
        return None

    return str(value)


def calculate_duration_seconds(time_open, time_close=None):
    """
    Calculate the duration between two datetime values in milliseconds.

    If time_close is None, it defaults to current UTC time (for open trades).
    Handles timezone-aware and naive datetimes.
    """
    # Normalize timezones
    time_open = time_open.astimezone() if time_open.tzinfo else time_open
    time_close = time_close.astimezone() if time_close and time_close.tzinfo else time_close

    # Default to now if time_close is None
    if time_close is None:
        time_close = datetime.now(timezone.utc)

    # Calculate time difference
    time_diff = time_close - time_open

    # Convert to seconds
    return time_diff.total_seconds()


def format_duration(seconds):
    # seconds = int(milliseconds)  # Convert ms to seconds
    days = seconds // 86400
    hours = (seconds % 86400) // 3600  # Get remaining hours after full days
    minutes = (seconds % 3600) // 60
    # secs = seconds % 60

    # Format the output dynamically (h, m, s only if nonzero)
    parts = []
    if days:
        parts.append(f"{days}d")
    if hours:
        parts.append(f"{hours}h")
    if minutes:
        parts.append(f"{minutes}m")
    # if secs or not parts:  # Always show seconds if no hours/minutes exist
    #     parts.append(f"{secs}s")

    return " ".join(parts)


def format_decimal(param):
    return "0" if param is None else f"{param:.2f}"


# Custom converter function to handle empty fields
def decimal_converter(val):
    if val == '' or pd.isna(val):  # Check if the value is empty or NaN
        return Decimal(0)  # You can also use Decimal('0') or any other placeholder
    return Decimal(val)


def clean_decimal(value):
    """Removes $ sign and converts value to Decimal safely."""
    if isinstance(value, str):
        value = value.replace("$", "").strip()  # Remove $ and spaces
    return value


def convert_to_utc(dt):
    if dt.tzinfo is None:
        # Assume naive time is in local timezone
        local_tz = pytz.timezone("America/New_York")
        dt = local_tz.localize(dt)

    return dt.astimezone(pytz.UTC)


def dateStringToDate(date_string, date_format='%Y-%m-%d %H:%M:%S'):
    if "+00:00" in date_string:
        date_string = date_string.replace("+00:00", "")

    # Parse naive datetime (assumed UTC)
    utc_naive = datetime.strptime(date_string, date_format)
    utc_aware = pytz.utc.localize(utc_naive)

    # Convert to New York time
    new_york_tz = pytz.timezone("America/New_York")
    return utc_aware.astimezone(new_york_tz)


def dateStringToMS(date_str, date_format='%Y-%m-%d %H:%M:%S'):
    date_obj = dateStringToDate(date_str, date_format)
    return date_to_ms(date_obj)


def date_to_ms(date):
    if date is None:
        return None
    return int(date.timestamp() * ms_multiplier)


def mSToDate(date_in_ms, multiplier=ms_multiplier):
    if date_in_ms is None:
        return None

    # Create naive UTC datetime
    utc_naive = datetime.utcfromtimestamp(date_in_ms / multiplier)
    utc_aware = pytz.utc.localize(utc_naive)

    # Convert to local timezone
    local_tz = pytz.timezone("America/New_York")
    return utc_aware.astimezone(local_tz)


def get_now_date():
    # Get current time in UTC
    utc_now = datetime.utcnow()

    # Localize to UTC properly
    utc_aware = pytz.utc.localize(utc_now)

    # Convert to your local timezone
    local_tz = pytz.timezone("America/New_York")
    local_date = utc_aware.astimezone(local_tz)

    return local_date


def formatDate(date, date_format='%m/%d/%y %I:%M%p'):  # '%m-%d-%Y %H:%M'
    if date is None:
        return None
    return date.strftime(date_format)


# Custom converter for date fields
def date_converter(val):
    if val == '' or pd.isna(val):
        return None  # Or a default date if you prefer
    return datetime.strptime(val, '%Y-%m-%d %H:%M:%S.%f')  # Modify format as per your date format


def ms_to_utc_date(start_ms):
    return datetime.fromtimestamp(start_ms / ms_multiplier, tz=timezone.utc)


def get_duration_since_last_import(last_update):
    if last_update:
        now = datetime.now(timezone.utc)
        duration_ms = calculate_duration_seconds(last_update, now)
        duration_str = format_duration(duration_ms / 1000)  # Assuming this returns "3h 14m" etc.
        return duration_str
    return None


# Utility to split list into chunks of n
def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def get_sierra_trades_file(path=None):
    """
    Retrieve the most recent Sierra Trades file from the specified path.
    Default is the user's desktop. Returns a DataFrame if the file is found, else None.
    """
    # Default to the user's desktop path if no path is provided
    if path is None:
        path = os.path.join(os.path.expanduser("~"), "Desktop")

    latest_file = None
    latest_date = None

    try:
        # Loop through files in the specified directory
        for filename in os.listdir(path):
            if filename.startswith("TradeActivityLogExport_") and filename.endswith(".csv"):
                date_str = filename.split("_")[1].replace(".csv", "")

                try:
                    # Parse the date
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")

                    # Update if this date is the latest we've seen
                    if latest_date is None or file_date > latest_date:
                        latest_date = file_date
                        latest_file = filename

                except ValueError:
                    continue  # Skip files with an invalid date format

        # Load the most recent file into a DataFrame, if found
        if latest_file:
            file_path = os.path.join(path, latest_file)
            # Using converters to apply decimal_converter to multiple columns
            decimal_columns = ['Quantity', 'FilledQuantity', 'HighDuringPosition', 'LowDuringPosition',
                               'PositionQuantity', 'FillPrice', 'Price', 'Price2', 'Quantity']
            date_columns = ['TransDateTime', 'DateTime']
            # Merge dictionaries without using update
            converters = {**{col: decimal_converter for col in decimal_columns},
                          **{col: date_converter for col in date_columns}}

            df = pd.read_csv(file_path, sep='\t', converters=converters)
            print(f"Loaded file: {latest_file}")
            return df
        else:
            print("No matching file found.")
            return None
    except FileNotFoundError:
        print(f"The directory {path} does not exist.")
        return None


def infer_date_format(date_str: str) -> str:
    try:
        # Parse the date string into a datetime object
        dt = parser.parse(date_str)

        # Define potential format components
        formats = {
            "%Y": dt.year != None,
            "%m": dt.month != None,
            "%d": dt.day != None,
            "%H": dt.hour != None and dt.hour != 0,
            "%M": dt.minute != None and dt.minute != 0,
            "%S": dt.second != None and dt.second != 0,
            "%f": dt.microsecond != None and dt.microsecond != 0
        }

        # Map detected parts to their positions in the input string
        format_str = date_str
        format_str = format_str.replace(str(dt.year), "%Y", 1) if "%Y" in formats else format_str
        format_str = format_str.replace(f"{dt.month:02d}", "%m", 1) if "%m" in formats else format_str
        format_str = format_str.replace(f"{dt.day:02d}", "%d", 1) if "%d" in formats else format_str
        format_str = format_str.replace(f"{dt.hour:02d}", "%H", 1) if "%H" in formats else format_str
        format_str = format_str.replace(f"{dt.minute:02d}", "%M", 1) if "%M" in formats else format_str
        format_str = format_str.replace(f"{dt.second:02d}", "%S", 1) if "%S" in formats else format_str
        format_str = format_str.replace(f"{dt.microsecond:06d}", "%f", 1) if "%f" in formats else format_str

        return format_str
    except Exception as e:
        return f"Error: {e}"

    # write a function that prints 1 to 100 to console
