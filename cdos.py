import time
from datetime import datetime, timezone

from dash import html

import helper
from models.coinbase_order import CoinbaseOrder
from models.order import Order, BuySell
from models.sierra_activity import SierraActivity
from models.trade import Trade, Exchange, TradeDirection
from models.trade import TradeStatus
from services import coinbase_advanced
from services.bybit import Bybit
from trades_db import TradesDB


# want a vibe code challenge? bottom is 10 day rolling normalization window of spot cvd and (perp cvd - spot cvd)
# with an open interest multiplier. spot above = buy, spot below = sell. gl.

# Marketing Angle Opportunities
#
# "The first truly private trading journal"
# "Your trading strategies stay yours - provably"
# "Built for traders who left CeFi for DeFi"
# "Journal your trades without journaling to us"


def get_new_orders(username, status, exchange, category: str = "linear", settleCoin: str = None):
    # Get the set of already processed order IDs
    status["status"] = "Getting Order history.."
    orders = []
    current_open_positions = []
    first_import = False
    match exchange:
        case Exchange.COINBASE:
            # For Coinbase, get open positions from the API
            current_open_positions = coinbase_advanced.get_futures_positions()
            orders = coinbase_advanced.get_coinbase_orders()
        case Exchange.BYBIT:
            # For Bybit, get open positions and orders
            current_open_positions, orders, first_import = Bybit.get_bybit_orders(category=category,
                                                                                  settleCoin=settleCoin,
                                                                                  username=username)

    status["status"] = f"Got {len(orders)} Orders and {len(current_open_positions)} Open Positions"

    # Process the orders with the current open positions
    count = process_orders(username, orders, status, current_open_positions, first_import)

    # Update the status with information about the last import
    last_import_date = TradesDB.get_coinbase_last_order_time() if exchange == Exchange.COINBASE else TradesDB.get_bybit_last_order_time()
    if last_import_date:
        duration_ms = helper.calculate_duration_seconds(last_import_date, datetime.now(timezone.utc))
        duration_since_last_import = helper.format_duration(duration_ms)
        status["status"] = (f"Done! Processed {html.Strong(count)} Orders!"
                            f"{html.Br}Last Order imported was {html.Strong(duration_since_last_import)} ✅")
    else:
        status["status"] = f"Error Processing Orders, processed {html.Strong(count)} Orders"


def process_orders(username, orders, status, current_open_positions, first_import):
    # Keep track of all trades we have created or updated so far
    built_trades = []

    # Create dict of open symbol/direction to open qty
    open_positions = {}
    for trade in current_open_positions:
        if trade.status == TradeStatus.OPEN:
            open_positions[trade.symbol + trade.direction.value] = trade.openQty

    count = 0
    for count, order in enumerate(orders, start=1):
        status["status"] = f"Processing Trade #{count}"
        order.printInfo()

        # Look for an open trade for this symbol in current_open_positions
        open_trade = get_open_trade(current_open_positions, order)

        if open_trade:
            if order.filled:
                # Extra quantity happens for Coinbase only when a user sells 3 with 1 open (-2) which results an
                # Automatically opening opposite position which is accounted for here
                extra_quantity = open_trade.update_with_order(order)

                if first_import:
                    # Check if open_trade (TRADE THAT IS OPEN NOW) quantity matches the open quantity in open_positions
                    # If it is, then we know we've processed all the opening orders for this open trade
                    if open_trade.openQty == open_positions.get(open_trade.symbol + open_trade.direction.value, None):
                        # Remove from open positions since its opening orders have been processed
                        current_open_positions.remove(open_trade)
                        del open_positions[open_trade.symbol + open_trade.direction.value]

                if open_trade.id_field is None:  # Trade created from open position during first import
                    TradesDB.saveTrade(open_trade)
                else:
                    if open_trade.status == TradeStatus.CLOSED:
                        # Unfilled orders placed outside the trade fill's timeframe need to be accounted for
                        add_unfilled_orders(orders, open_trade)
                        open_trade.calculateRisk()
                    TradesDB.updateTrade(open_trade)

                # COINBASE ONLY: Now handle extra QTY if needed (They open new positions automatically in some cases)
                if extra_quantity < 0:
                    open_next_coinbase_order(built_trades, current_open_positions, extra_quantity, order)
            else:
                open_trade.unfilled_orders.append(order)
                open_trade.sortOrders()
                # order.trade_id = open_trade.trade_id
                # TradesDB.saveOrder(od=order)
                open_trade.calculateRisk()
                if open_trade.id_field is None:  # Trade created from open position during first import
                    TradesDB.saveTrade(open_trade)
                else:
                    TradesDB.updateTrade(open_trade)
        # reduce orders cannot open trades, we are probably missing the previous leg of this trade
        elif order.filled and (first_import or not order.reduce):
            # Create a new trade directly without trying to match to closed trades
            newTrade = Trade.fromOrder(order=order, username=username)
            TradesDB.saveTrade(newTrade)
            # Add the new trade to current_open_positions if it's open
            if newTrade.status == TradeStatus.OPEN:
                current_open_positions.append(newTrade)
            # Keep track of all trades we have created so far
            built_trades.append(newTrade)

    status["status"] = f"Done! Processed {count} Trades"
    return count


def get_open_trade(current_open_positions, order):
    open_trade = [
        t for t in current_open_positions
        if (t.status == TradeStatus.OPEN and
            normalize_symbol(t.symbol) == normalize_symbol(order.symbol) and
            ((order.reduce and
              ((order.buySell == BuySell.BUY and t.direction == TradeDirection.SHORT) or
               (order.buySell == BuySell.SELL and t.direction == TradeDirection.LONG))) or
             (not order.reduce and
              ((order.buySell == BuySell.BUY and t.direction == TradeDirection.LONG) or
               (order.buySell == BuySell.SELL and t.direction == TradeDirection.SHORT)))))
    ]
    if open_trade:
        return open_trade[0]
    else:
        return None

def open_next_coinbase_order(built_trades, current_open_positions, extra_quantity, order):
    # Clone Order with remaining quantity, open new trade with that order
    extra_cbo_order = CoinbaseOrder.from_existing(order, extra_quantity)
    extra_order = Order.fromCoinbaseOrder(extra_cbo_order)
    newTrade = Trade.fromOrder(order=extra_order)
    TradesDB.saveTrade(newTrade)
    # Add the new trade to current_open_positions
    current_open_positions.append(newTrade)
    # Keep track of all trades we have created so far
    built_trades.append(newTrade)


def add_unfilled_orders(orders, open_trade):
    """
    Add unfilled orders to a trade.

    Args:
        orders: List of Order objects to check for unfilled orders
        open_trade: The Trade object to add unfilled orders to
    """
    # Process for both open and closed trades
    if open_trade.trade_orders and any(order.filled for order in open_trade.trade_orders):
        # Find the time range for the trade's filled orders
        filled_orders = [order for order in open_trade.trade_orders if order.filled]

        first_filled_order_created_time = min(
            filled_orders,
            key=lambda order: order.created_date,
            default=None
        ).created_date

        last_filled_order_created_time = max(
            filled_orders,
            key=lambda order: order.created_date,
            default=None
        ).created_date

        # Ensure we have first_filled_order_created_time and last_filled_order_created_time
        if not first_filled_order_created_time or not last_filled_order_created_time:
            return

        # Find all unfilled orders that were created during the trade's lifetime
        new_unfilled_orders = []

        for order in orders:
            # Skip filled orders
            if order.filled:
                continue

            # Skip orders for different symbols
            if normalize_symbol(order.symbol) != normalize_symbol(open_trade.symbol):
                continue

            # Check if the order was created during the trade's lifetime
            if not (first_filled_order_created_time < order.created_date < last_filled_order_created_time):
                continue

            # Avoid duplicates by checking if the order is already in unfilled_orders
            if any(uo.order_id == order.order_id for uo in open_trade.unfilled_orders):
                continue

            # Add the order to the trade
            new_unfilled_orders.append(order)

        # Add the new unfilled orders to the existing ones
        open_trade.unfilled_orders.extend(new_unfilled_orders)

        # Re-Sort orders
        open_trade.sortOrders()


def normalize_symbol(symbol):
    if symbol.endswith("-USDT"):
        return symbol.replace("-USDT", "-USD")
    elif symbol.endswith("-USDC"):
        return symbol.replace("-USDC", "-USD")
    return symbol

# Program Start #
# result = Bybit.getExecutions()


# SIERRA CONTROLS #

# df = Helper.get_sierra_trades_file()
# processSierraActivities(df)

########################

# COINBASE CONTROLS #

# cos = Coinbase.get_futures_positions()
# Coinbase.get_all_conversions()

# if not TradesDB.check_db_exists():
#     TradesDB.createTradeDB()

# xlm_account_id = CoinbaseWallet.get_xlm_account_id()
# print(f"Found XLM Account ID: {xlm_account_id}")
#
# transactions = CoinbaseWallet.get_transactions(xlm_account_id)

########################
